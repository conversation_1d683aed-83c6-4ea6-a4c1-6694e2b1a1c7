// TypeBox schema for conditions service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, queryWrapper, commonPatch } from '../../utils/common/typebox-schemas.js'

export const conditionsSchema = Type.Object({
  _id: ObjectIdSchema(),
  standard: Type.Optional(Type.String()),
  code: Type.Optional(Type.String()),
  link: Type.Optional(Type.String()),
  chapter: Type.Optional(Type.String()),
  name: Type.Optional(Type.String()),
  description: Type.Optional(Type.String()),
  type: Type.Optional(Type.String()),
  procedures: Type.Optional(Type.Array(ObjectIdSchema())),
  procedureComments: Type.Optional(Type.String()),
  ...commonFields
}, { $id: "Conditions", additionalProperties: false })

export type Conditions = Static<typeof conditionsSchema>
export const conditionsValidator = getValidator(conditionsSchema, dataValidator)
export const conditionsResolver = resolve<Conditions, HookContext>({})
export const conditionsExternalResolver = resolve<Conditions, HookContext>({})

export const conditionsDataSchema = Type.Object({
  ...Type.Omit(conditionsSchema, ['_id']).properties
}, { additionalProperties: false })

export type ConditionsData = Static<typeof conditionsDataSchema>
export const conditionsDataValidator = getValidator(conditionsDataSchema, dataValidator)
export const conditionsDataResolver = resolve<ConditionsData, HookContext>({})

// Pick ObjectId fields and nested ObjectId fields for query properties
const conditionsQueryProperties = Type.Pick(conditionsSchema, ['_id', 'procedures', 'createdBy', 'updatedBy'])

export const conditionsPatchSchema = commonPatch(conditionsSchema, { pushPullOpts: [], pickedForSet: conditionsQueryProperties })
export type ConditionsPatch = Static<typeof conditionsPatchSchema>
export const conditionsPatchValidator = getValidator(conditionsPatchSchema, dataValidator)
export const conditionsPatchResolver = resolve<ConditionsPatch, HookContext>({})
export const conditionsQuerySchema = queryWrapper(Type.Object({
  ...conditionsQueryProperties.properties
}, { additionalProperties: true }))
export type ConditionsQuery = Static<typeof conditionsQuerySchema>
export const conditionsQueryValidator = getValidator(conditionsQuerySchema, queryValidator)
export const conditionsQueryResolver = resolve<ConditionsQuery, HookContext>({})
