// TypeBox schema for doc-templates service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, commonPatch, queryWrapper } from '../../utils/common/typebox-schemas.js'
import {sectionsSchema} from '../plan-docs/utils/schemas.js';

export const docTemplatesSchema = Type.Object({
  _id: ObjectIdSchema(),
  name: Type.String(),
  description: Type.Optional(Type.String()),
  class: Type.Optional(Type.Union([
    Type.Literal("core"),
    Type.Literal("125"),
    Type.Literal("105"),
    Type.Literal("misc"),
    Type.Literal("spd")
  ])),
  subClass: Type.Optional(Type.String()),
  sections: sectionsSchema,
  smb: Type.Optional(Type.Boolean()),
  ...commonFields
}, { $id: 'DocTemplates', additionalProperties: false })

export type DocTemplates = Static<typeof docTemplatesSchema>
export const docTemplatesValidator = getValidator(docTemplatesSchema, dataValidator)
export const docTemplatesResolver = resolve<DocTemplates, HookContext>({})
export const docTemplatesExternalResolver = resolve<DocTemplates, HookContext>({})

export const docTemplatesDataSchema = Type.Object({
  ...Type.Omit(docTemplatesSchema, ['_id']).properties
}, { additionalProperties: false })

export type DocTemplatesData = Static<typeof docTemplatesDataSchema>
export const docTemplatesDataValidator = getValidator(docTemplatesDataSchema, dataValidator)
export const docTemplatesDataResolver = resolve<DocTemplatesData, HookContext>({})

// Pick ObjectId fields and nested ObjectId fields for query properties
const docTemplatesQueryProperties = Type.Pick(docTemplatesSchema, ['_id', 'sections'])

export const docTemplatesPatchSchema = commonPatch(docTemplatesSchema, { pushPullOpts: [], pickedForSet: docTemplatesQueryProperties })
export type DocTemplatesPatch = Static<typeof docTemplatesPatchSchema>
export const docTemplatesPatchValidator = getValidator(docTemplatesPatchSchema, dataValidator)
export const docTemplatesPatchResolver = resolve<DocTemplatesPatch, HookContext>({})

export const docTemplatesQuerySchema = queryWrapper(docTemplatesSchema)
export type DocTemplatesQuery = Static<typeof docTemplatesQuerySchema>
export const docTemplatesQueryValidator = getValidator(docTemplatesQuerySchema, queryValidator)
export const docTemplatesQueryResolver = resolve<DocTemplatesQuery, HookContext>({})
