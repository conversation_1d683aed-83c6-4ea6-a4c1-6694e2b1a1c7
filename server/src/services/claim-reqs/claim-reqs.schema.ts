// TypeBox schema for claim-reqs service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, queryWrapper, imageSchema, commonPatch } from '../../utils/common/typebox-schemas.js'

export const claimReqsSchema = Type.Object({
  _id: ObjectIdSchema(),
  plan: Type.Optional(ObjectIdSchema()),
  org: Type.Optional(ObjectIdSchema()),
  patient: Type.Optional(ObjectIdSchema()),
  person: Type.Optional(ObjectIdSchema()),
  care: Type.Optional(ObjectIdSchema()),
  visit: Type.Optional(ObjectIdSchema()),
  claim: Type.Optional(ObjectIdSchema()),
  provider: Type.Optional(ObjectIdSchema()),
  practitioner: Type.Optional(ObjectIdSchema()),
  providerOrg: Type.Optional(ObjectIdSchema()),
  threads: Type.Optional(Type.Array(ObjectIdSchema())),
  files: Type.Optional(Type.Record(Type.String(), imageSchema)),
  status: Type.Optional(Type.Union([
    Type.Literal("unopened"),
    Type.Literal("pending"),
    Type.Literal("approved"),
    Type.Literal("rejected")
  ])),
  removeRequest: Type.Optional(Type.Boolean()),
  ...commonFields
}, { $id: 'ClaimReqs', additionalProperties: false })

export type ClaimReqs = Static<typeof claimReqsSchema>
export const claimReqsValidator = getValidator(claimReqsSchema, dataValidator)
export const claimReqsResolver = resolve<ClaimReqs, HookContext>({})
export const claimReqsExternalResolver = resolve<ClaimReqs, HookContext>({})

export const claimReqsDataSchema = Type.Object({
  ...Type.Omit(claimReqsSchema, ['_id']).properties
}, { additionalProperties: false })

export type ClaimReqsData = Static<typeof claimReqsDataSchema>
export const claimReqsDataValidator = getValidator(claimReqsDataSchema, dataValidator)
export const claimReqsDataResolver = resolve<ClaimReqsData, HookContext>({
  status: async (val) => {
    if(!val) return 'unopened';
    return val
  }

})

// Pick ObjectId fields and nested ObjectId fields for query properties
const claimReqsQueryProperties = Type.Pick(claimReqsSchema, ['_id', 'plan', 'org', 'patient', 'person', 'care', 'visit', 'claim', 'provider', 'practitioner', 'providerOrg', 'threads', 'files'])

export const claimReqsPatchSchema = commonPatch(claimReqsSchema, { pushPullOpts: [
  { path: 'files', type: imageSchema }
], pickedForSet: claimReqsQueryProperties })
export type ClaimReqsPatch = Static<typeof claimReqsPatchSchema>
export const claimReqsPatchValidator = getValidator(claimReqsPatchSchema, dataValidator)
export const claimReqsPatchResolver = resolve<ClaimReqsPatch, HookContext>({})
export const claimReqsQuerySchema = queryWrapper(claimReqsQueryProperties)
export type ClaimReqsQuery = Static<typeof claimReqsQuerySchema>
export const claimReqsQueryValidator = getValidator(claimReqsQuerySchema, queryValidator)
export const claimReqsQueryResolver = resolve<ClaimReqsQuery, HookContext>({})
