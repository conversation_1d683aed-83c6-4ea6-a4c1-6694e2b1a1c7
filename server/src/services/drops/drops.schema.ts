// TypeBox schema for drops service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, commonPatch, queryWrapper } from '../../utils/common/typebox-schemas.js'

export const dropsSchema = Type.Object({
  _id: ObjectIdSchema(),
  tags: Type.Optional(Type.Array(Type.String())),
  type: Type.Optional(Type.Union([Type.Literal("question"), Type.Literal("answer")])),
  archives: Type.Optional(Type.Object({
    title: Type.Optional(Type.Array(Type.String())),
    body: Type.Optional(Type.Array(Type.String()))
  })),
  topAnswer: Type.Optional(ObjectIdSchema()),
  topScore: Type.Optional(Type.Number()),
  title: Type.Optional(Type.String()),
  body: Type.Optional(Type.String()),
  class: Type.Optional(Type.String()),
  anonymous: Type.Optional(Type.Boolean()),
  threads: Type.Optional(Type.Array(ObjectIdSchema())),
  voteCount: Type.Optional(Type.Number()),
  upVotes: Type.Optional(Type.Array(ObjectIdSchema())),
  downVotes: Type.Optional(Type.Array(ObjectIdSchema())),
  ...commonFields
}, { $id: 'Drops', additionalProperties: false })

export type Drops = Static<typeof dropsSchema>
export const dropsValidator = getValidator(dropsSchema, dataValidator)
export const dropsResolver = resolve<Drops, HookContext>({})
export const dropsExternalResolver = resolve<Drops, HookContext>({})

export const dropsDataSchema = Type.Object({
  ...Type.Omit(dropsSchema, ['_id']).properties
}, { additionalProperties: false })

export type DropsData = Static<typeof dropsDataSchema>
export const dropsDataValidator = getValidator(dropsDataSchema, dataValidator)
export const dropsDataResolver = resolve<DropsData, HookContext>({})

// Pick ObjectId fields and nested ObjectId fields for query properties
const dropsQueryProperties = Type.Pick(dropsSchema, ['_id', 'topAnswer', 'threads', 'upVotes', 'downVotes'])

export const dropsPatchSchema = commonPatch(dropsSchema, { pushPullOpts: [{ path: 'threads', type: ObjectIdSchema() }], pickedForSet: dropsQueryProperties })
export type DropsPatch = Static<typeof dropsPatchSchema>
export const dropsPatchValidator = getValidator(dropsPatchSchema, dataValidator)
export const dropsPatchResolver = resolve<DropsPatch, HookContext>({})

export const dropsQuerySchema = queryWrapper(dropsSchema)
export type DropsQuery = Static<typeof dropsQuerySchema>
export const dropsQueryValidator = getValidator(dropsQuerySchema, queryValidator)
export const dropsQueryResolver = resolve<DropsQuery, HookContext>({})
