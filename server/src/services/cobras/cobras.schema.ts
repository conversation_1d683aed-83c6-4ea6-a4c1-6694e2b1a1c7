// TypeBox schema for cobras service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, queryWrapper, commonPatch } from '../../utils/common/typebox-schemas.js'

export const cobrasSchema = Type.Object({
  _id: ObjectIdSchema(),
  enrollment: ObjectIdSchema(),
  participant: ObjectIdSchema(),
  household: ObjectIdSchema(),
  spec: Type.Optional(ObjectIdSchema()),
  event_type: Type.Optional(
    Type.Union([
      Type.Literal("job"),
      Type.Literal("death"),
      Type.Literal("divorce"),
      Type.Literal("medicare"),
      Type.Literal("dependent"),
    ])
  ),
  deadline: Type.String(),
  end_date: Type.Optional(Type.String()),
  optOut: Type.Optional(Type.Boolean()),
  coverages: Type.Optional(
    Type.Record(Type.String(), Type.Object({
      recurs: Type.Optional(Type.Number()),
      participants: Type.Optional(Type.Array(ObjectIdSchema()))
    }))
  ),
  ...commonFields
}, { $id: "Cobras", additionalProperties: false })

export type Cobras = Static<typeof cobrasSchema>
export const cobrasValidator = getValidator(cobrasSchema, dataValidator)
export const cobrasResolver = resolve<Cobras, HookContext>({})
export const cobrasExternalResolver = resolve<Cobras, HookContext>({})

export const cobrasDataSchema = Type.Object({
  ...Type.Omit(cobrasSchema, ['_id']).properties
}, { additionalProperties: false })

export type CobrasData = Static<typeof cobrasDataSchema>
export const cobrasDataValidator = getValidator(cobrasDataSchema, dataValidator)
export const cobrasDataResolver = resolve<CobrasData, HookContext>({})

// Pick ObjectId fields and nested ObjectId fields for query properties
const cobrasQueryProperties = Type.Pick(cobrasSchema, ['_id', 'enrollment', 'participant', 'household', 'spec', 'createdBy', 'updatedBy'])

export const cobrasPatchSchema = commonPatch(cobrasSchema, { pushPullOpts: [], pickedForSet: cobrasQueryProperties })
export type CobrasPatch = Static<typeof cobrasPatchSchema>
export const cobrasPatchValidator = getValidator(cobrasPatchSchema, dataValidator)
export const cobrasPatchResolver = resolve<CobrasPatch, HookContext>({})
export const cobrasQuerySchema = queryWrapper(Type.Object({
  ...cobrasQueryProperties.properties
}, { additionalProperties: true }))
export type CobrasQuery = Static<typeof cobrasQuerySchema>
export const cobrasQueryValidator = getValidator(cobrasQuerySchema, queryValidator)
export const cobrasQueryResolver = resolve<CobrasQuery, HookContext>({})
