// TypeBox schema for coverages service
import {resolve} from '@feathersjs/schema'
import {Type, getValidator, ObjectIdSchema} from '@feathersjs/typebox'
import type {Static} from '@feathersjs/typebox'
import type {HookContext} from '../../declarations.js'
import {dataValidator, queryValidator} from '../../validators.js'
import {commonFields, queryWrapper, commonPatch} from '../../utils/common/typebox-schemas.js'
import {costLimits} from '../plans/utils/rules.js';


import {coverCopySchema} from './schemas/copy.js'

/** Adjudication flow can simply be: this coverage has the following special deductible categories - does this claim match any of the following */

// Main data model schema
export const coveragesSchema = Type.Object(
    {
        _id: ObjectIdSchema(),

        vectorIds: Type.Optional(
            Type.Record(
                Type.String(),
                Type.Object(
                    {
                        uploadIds: Type.Optional(Type.Array(ObjectIdSchema())),
                        id: Type.Optional(Type.String()),
                        fileIds: Type.Optional(Type.Array(Type.String())), // Vector store IDs
                        updatedAt: Type.Optional(Type.Any()),
                    },
                    {additionalProperties: true}
                )
            )
        ),

        // bring in coverCopySchema properties (already TypeBox)
        ...coverCopySchema.properties,

        // bring in commonFields properties (already TypeBox)
        ...commonFields,
    },
    {
        $id: 'Coverages',
        additionalProperties: false,
        required: ['_id', 'name', 'covered', 'type'],
    }
);

export type Coverages = Static<typeof coveragesSchema>
export const coveragesValidator = getValidator(coveragesSchema, dataValidator)

const limitOop = (val) => {
    if (!val) return val;
    const {single = 0, family = 0} = val;
    return {
        single: Math.min(single, costLimits.moop.single),
        family: Math.min(family, costLimits.moop.family),
        type: val.type || 'annual'
    }
}

export const coveragesResolver = resolve<Coverages, HookContext>({
    properties: {
        moop: async (val) => {
            return limitOop(val);
        },
        deductible: async (val) => {
            return limitOop(val)
        }
    }
})
export const coveragesExternalResolver = resolve<Coverages, HookContext>({})

// Schema for creating new data
export const coveragesDataSchema = Type.Object({
    ...Type.Omit(coveragesSchema, ['_id']).properties
}, {additionalProperties: false})

export type CoveragesData = Static<typeof coveragesDataSchema>
export const coveragesDataValidator = getValidator(coveragesDataSchema, dataValidator)
export const coveragesDataResolver = resolve<CoveragesData, HookContext>({})

const coveragesQueryProperties = Type.Pick(coveragesSchema, ['_id', 'carrierLogo', 'documents', 'createdBy', 'updatedBy', 'issuer', 'org', 'provider', 'fromTemplate', 'contract'])

// Schema for updating existing data
export const coveragesPatchSchema = commonPatch(coveragesSchema, {
    pushPullOpts: [{path: 'rates', type: ObjectIdSchema()}],
    pickedForSet: coveragesQueryProperties
})
export type CoveragesPatch = Static<typeof coveragesPatchSchema>
export const coveragesPatchValidator = getValidator(coveragesPatchSchema, dataValidator)
export const coveragesPatchResolver = resolve<CoveragesPatch, HookContext>({})


export const coveragesQuerySchema = queryWrapper(Type.Object({
    ...coveragesQueryProperties.properties, 'carrierLogo.uploadId': Type.Optional(Type.Union([
        ObjectIdSchema(),
        Type.Object(
            {
                $in: Type.Optional(Type.Array(ObjectIdSchema())),
                $nin: Type.Optional(Type.Array(ObjectIdSchema())),
            },
            {additionalProperties: true}
        ),
    ]))
}, {additionalProperties: true}))
export type CoveragesQuery = Static<typeof coveragesQuerySchema>
export const coveragesQueryValidator = getValidator(coveragesQuerySchema, queryValidator)
export const coveragesQueryResolver = resolve<CoveragesQuery, HookContext>({})
