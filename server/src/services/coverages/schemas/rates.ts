import {Type} from '@feathersjs/typebox';

const rateSchema = Type.Optional(
    Type.Record(Type.String({ pattern: "^([0-9]|[1-9][0-9]|1[01][0-9]|120)$" }), Type.Number())
)
const rateByAge = Type.Record(
    Type.String({pattern: "^(?:[0-9]|[1-9][0-9]|1[01][0-9]|120)$"}),
    Type.Number(),
    {
        description:
            'flat rate by age - key is the age, value is the rate. This is how ACA plans are priced. For multi-person discounts use multiDiscount.'
    }
);

const fixedRate = Type.Object(
    {
        single: Type.Optional(Type.Number({ description: 'rate for a single person' })),
        plus_spouse: Type.Optional(Type.Number({ description: 'rate for a single person plus a spouse' })),
        plus_child: Type.Optional(Type.Number({ description: 'rate for a single person plus a child' })),
        plus_child__2: Type.Optional(Type.Number({ description: 'rate for a single person plus 2 children' })),
        plus_child__3: Type.Optional(Type.Number({ description: 'rate for a single person plus 3 children' })),
        family: Type.Optional(Type.Number({ description: 'rate for a family' }))
    },
    { additionalProperties: true }
);

const fixedRates = Type.Record(
    Type.String({ pattern: '^([0-9]|[1-9][0-9]|1[01][0-9]|120)$' }), // keys "0".."120"
    fixedRate,
    {
        description:
            'this is the structure for coverages that set the premium based on the oldest/head of household age - a simpler way of calculating rates vs age of every household member see fixedRates schema.'
    }
);

const premiumStructures = Type.Object({
    flatPremium: Type.Optional(Type.Object(fixedRate.properties, { description: 'if the premium is a flat number per person, no age banding, no multi-person discount' })),
    rateByAge: Type.Optional(rateByAge),
    fixedRates: Type.Optional(fixedRates),
}, { description: 'Each coverage chooses one of these 3 rate structures. All amounts are monthly' });

const premiumUtils = Type.Object({
    rateType: Type.Optional(Type.String({ enum: ['flatPremium', 'rateByAge', 'fixedRates'], description: 'which premium structure is being used - default is flatPremium if set and then fixedRates if set then rateByAge. Can use with multi-discount for a discount to apply based on number of enrollees' })),
    multiDiscount: Type.Optional(Type.Object(rateSchema.patternProperties, { description: 'Used to discount rateByAge for multi-person households. So {1:1, 2:.1, 3:.2} means 10% discount on the total rate at 2 people, 20$ discount at 3 people, etc.' })),
    weights: Type.Optional(Type.Object(rateSchema.patternProperties, { description: 'A complex option for weighting rates by age such that multiDiscount doesn\'t apply equally to all ages' })),
    baseDefault: Type.Optional(Type.Boolean({ description: 'To be used with flatPremium - if true, this is the default rate if no rates are found' })),
    breakpointAges: Type.Optional(Type.Array(Type.Number(), { description: 'This is used to manage generating rates for fixedRates with rateBreak set to breakpoint. This tells us which ages are a new breakpoint and we auto-fill all the ages in that age-band with the same fixed rates' })),
    rateBreak: Type.Optional(Type.String({ enum: ['graduated', 'breakpoint'], description: 'Used with fixedRates. How the rates are broken up - graduated is a new rate for each age, breakpoint is a rate for each age group on the breakpointAges property' })),
    smokerFactor: Type.Optional(Type.Number({ description: 'increase percentage for smoking status ie: 1.5 would be 50% more for smokers' }))
}, { description: 'These are settings for adjusting the rates in the chosen premium structure based on count, age, etc.' });

export const premiumSchema = Type.Object({
    ...premiumStructures.properties,
    ...premiumUtils.properties
}, { description: 'Premium structure for a coverage' });
