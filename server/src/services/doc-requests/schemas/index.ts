import {ObjectIdSchema, Type} from '@feathersjs/typebox';

export const eeFactsSchema = Type.Object({
    gender: Type.Optional(Type.Union([Type.Literal("male"), Type.Literal("female")])),
    wage: Type.Optional(Type.Number()),
    income: Type.Optional(Type.Number()),
    hh_income: Type.Optional(Type.Number()),
    hourly: Type.Optional(Type.Boolean()),
    hours: Type.Optional(Type.Number()),
    married: Type.Optional(Type.String()),
    deps: Type.Optional(Type.Number()),
    zip: Type.Optional(Type.String()),
    state: Type.Optional(Type.String()),
    smoker: Type.Optional(Type.Boolean()),
    spouseAge: Type.Optional(Type.String()),
    spouseDob: Type.Optional(Type.String()),
    age: Type.Optional(Type.Number()),
    dob: Type.Optional(Type.String()),
})
export const eeSchema = Type.Object({
    w9: Type.Optional(Type.Boolean()),
    role: Type.Optional(Type.String()),
    errorAdding: Type.Optional(Type.Boolean()),
    addError: Type.Optional(Type.String()),
    camsError: Type.Optional(Type.Boolean()),
    uid: Type.Optional(Type.String()),
    name: Type.Optional(Type.String()),
    email: Type.Optional(Type.String()),
    lastName: Type.Optional(Type.String()),
    firstName: Type.Optional(Type.String()),
    person: Type.Optional(ObjectIdSchema()),
    ...eeFactsSchema.properties,
    updatedAt: Type.Optional(Type.Any()),
    ptc: Type.Optional(Type.Number()),
    ptcUpdatedAt: Type.Optional(Type.Any()),
    countyfips: Type.Optional(Type.String()),
    lastFacts: Type.Optional(eeFactsSchema)
})
