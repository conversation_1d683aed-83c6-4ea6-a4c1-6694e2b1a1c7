// TypeBox schema for doc-requests service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, commonPatch, queryWrapper, phoneSchema, imageSchema } from '../../utils/common/typebox-schemas.js'

import { eeSchema } from './schemas/index.js';

export const docRequestsSchema = Type.Object({
  _id: ObjectIdSchema(),
  types: Type.Optional(Type.Array(Type.String())),
  orgName: Type.Optional(Type.String()),
  orgAvatar: Type.Optional(Type.String()),
  org: Type.Optional(ObjectIdSchema()),
  plan: Type.Optional(ObjectIdSchema()),
  fingerprint: Type.Optional(Type.String()),
  refName: Type.Optional(Type.String()),
  doc: Type.Optional(Type.String()),
  eeCount: Type.Optional(Type.Number()),
  fteCount: Type.Optional(Type.Number()),
  email: Type.Optional(Type.String()),
  phone: Type.Optional(phoneSchema),
  name: Type.Optional(Type.String()),
  optIn: Type.Optional(Type.Boolean()),
  status: Type.Optional(Type.Union([Type.Literal("complete"), Type.Literal("partial")])),
  states: Type.Optional(Type.Array(Type.String())),
  docType: Type.Optional(Type.Union([Type.Literal("smb")])),
  employees: Type.Optional(Type.Array(eeSchema)),
  files: Type.Optional(Type.Record(Type.String(), imageSchema)),
  ...commonFields
}, { $id: 'DocRequests', additionalProperties: true })

export type DocRequests = Static<typeof docRequestsSchema>
export const docRequestsValidator = getValidator(docRequestsSchema, dataValidator)
export const docRequestsResolver = resolve<DocRequests, HookContext>({})
export const docRequestsExternalResolver = resolve<DocRequests, HookContext>({})

export const docRequestsDataSchema = Type.Object({
  ...Type.Omit(docRequestsSchema, ['_id']).properties
}, { additionalProperties: false })

export type DocRequestsData = Static<typeof docRequestsDataSchema>
export const docRequestsDataValidator = getValidator(docRequestsDataSchema, dataValidator)
export const docRequestsDataResolver = resolve<DocRequestsData, HookContext>({})

// Pick ObjectId fields and nested ObjectId fields for query properties
const docRequestsQueryProperties = Type.Pick(docRequestsSchema, ['_id', 'org', 'plan', 'employees'])

export const docRequestsPatchSchema = commonPatch(docRequestsSchema, { pushPullOpts: [], pickedForSet: docRequestsQueryProperties })
export type DocRequestsPatch = Static<typeof docRequestsPatchSchema>
export const docRequestsPatchValidator = getValidator(docRequestsPatchSchema, dataValidator)
export const docRequestsPatchResolver = resolve<DocRequestsPatch, HookContext>({})

export const docRequestsQuerySchema = queryWrapper(docRequestsSchema)
export type DocRequestsQuery = Static<typeof docRequestsQuerySchema>
export const docRequestsQueryValidator = getValidator(docRequestsQuerySchema, queryValidator)
export const docRequestsQueryResolver = resolve<DocRequestsQuery, HookContext>({})


