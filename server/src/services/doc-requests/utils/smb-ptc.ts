import {HookContext} from '../../../declarations.js';
import {CoreCall} from 'feathers-ucan';

const getPerson = ({
                       age, child, smoker, gender
                   }: any) => {
    return {
        age,
        child: !!child,
        smoker: !!smoker,
        gender
    }
}
type EstimateIncomeOptions = {
    income: number;
    married: boolean;
    deps: number;
    rng?: () => number; // optional custom random number generator for testing
};

const depTable: Record<number, Record<number, number>> = {
    50000: { 1: 0.45, 2: 0.57, 3: 0.65, 4: 0.75, 15: 0.9 },
    75000: { 1: 0.3, 2: 0.5, 3: 0.6, 4: 0.75, 15: 0.9 },
    100000: { 1: 0.2, 2: 0.4, 3: 0.55, 4: 0.65, 15: 0.75 },
    150000: { 1: 0.2, 2: 0.4, 3: 0.5, 4: 0.6, 15: 0.65 },
    100000000: { 1: 0.2, 2: 0.4, 3: 0.5, 4: 0.6, 15: 0.65 }
};

const estimateHouseholdIncome = ({
                                     income,
                                     married,
                                     deps,
                                     rng = Math.random
                                 }: EstimateIncomeOptions): number => {
    if (!married) return income;

    let odds = 0.68; // base odds
    const incomeBrackets = Object.keys(depTable).map(Number).sort((a, b) => a - b);

    for (const bracket of incomeBrackets) {
        if (income < bracket) {
            const depBrackets = Object.keys(depTable[bracket]).map(Number).sort((a, b) => a - b);
            for (const depBracket of depBrackets) {
                if (deps <= depBracket) {
                    odds = depTable[bracket][depBracket];
                    break;
                }
            }
            break;
        }
    }

    if (rng() > odds) return income;

    // Estimate 2nd income as a fraction (0.3–1.0x) of primary
    const fraction = 0.3 + rng() * 0.7;
    const secondIncome = income * fraction;

    return income + secondIncome;
};


type GenerateHHOptions = {
    age: number,
    married: boolean,
    deps: number,
    smoker?: boolean
}
const generateHh = ({age, married, deps, smoker}: GenerateHHOptions) => {
    const people = [getPerson({
        age, child: false, smoker, gender: 'Male'
    })]
    if (married) people.push(getPerson({
        age, gender: 'Female'
    }))
    for (let i = 0; i < deps; i++) {
        people.push(getPerson({age: 10, child: true}))
    }
    return people;
}


import {eeFactsSchema} from '../schemas/indes.js'
import {getAptc} from '../../marketplace/utils/hooks.js';
import {marketplaceDefMap, nonMarketplaceStates} from '../../marketplace/cms/index.js';

const defs = {
    'string': undefined,
    'boolean': false,
    'number': 0
}
export const smbPtc = () => {
    return async (context: HookContext) => {
        const {smb_ptc} = context.params.runJoin || {};
        if (smb_ptc) {

            const ees = context.result.employees || []

            const zipDrawers: any = {};
            const errs: any = {};
            let needPatch = false

            const factMatch = (ee: any) => {
                if (!ee.lastFacts) return false;
                const keys = Object.keys(eeFactsSchema.properties);
                let match = true;
                for (let i = 0; i < keys.length; i++) {
                    if (ee[keys[i]] !== ee.lastFacts[keys[i]] || defs[eeFactsSchema.properties[keys[i]].type]) {
                        match = false;
                        break;
                    }
                }
                return match;
            }
            const setFacts = (ee: any) => {
                const keys = Object.keys(eeFactsSchema.properties);
                const facts: any = {};
                const eeKeys = Object.keys(ee);
                for (let i = 0; i < keys.length; i++) {
                    if (eeKeys.includes(keys[i])) {
                        facts[keys[i]] = ee[keys[i]];
                    }
                }
                return facts;
            }
            // GET ZIP CODES THAT NEED TO BE RE-LOOKED AT FOR CHANGES AND API PLACE DATA
            const unmatchedZips: any = [];
            for (let i = 0; i < ees.length; i++) {
                const ee = ees[i];
                if (ee.zip && !ee.countyfips || !ee.state || !factMatch(ee)) unmatchedZips.push(ee.zip);
            }

            if (unmatchedZips.length > 0) {
                const junk = await new CoreCall('junk-drawers', context).find({
                    query: {
                        $limit: unmatchedZips.length,
                        itemId: {$in: unmatchedZips.map(a => `zips|${a.slice(0, 3)}`)}
                    }
                })
                    .catch(err => {
                        console.error(`Error getting zips: ${err.message}`)
                        return {data: []}
                    })

                for (let i = 0; i < junk.data.length; i++) {
                    const jd = junk.data[i];
                    zipDrawers[jd.itemName] = jd.data
                }
            }

            const onePtc = async (ee) => {
                if (!ee) return 0;
                const factCheck = factMatch(ee);
                if (smb_ptc.redo || !ee.updatedAt || !ee.ptcUpdatedAt || !factCheck) {

                    let eeIdx = -1;

                    const household = {
                        income: estimateHouseholdIncome({income: ee.income, married: ee.married === 'Y', deps: ee.deps}),
                        has_married_couple: ee.married === 'Y',
                        people: generateHh({
                            age: ee.age,
                            married: ee.married === 'Y',
                            deps: ee.deps,
                            smoker: ee.smoker || undefined
                        }),
                    }

                    for (let i = 0; i < ees.length; i++) {
                        if (ees[i].uid === ee.uid) {
                            eeIdx = i;
                            if (ee.hh_income !== household.income) {
                                needPatch = true;
                                ees[i].hh_income = household.income;
                                ees[i].lastFacts = setFacts(ees[i])
                            } else if(!factCheck) {
                                ees[i].lastFacts = setFacts(ees[i])
                                needPatch = true;
                            }
                        }
                    }

                    let place = {
                        countyfips: ee.countyfips,
                        state: ee.state,
                        zipcode: ee.zip
                    }
                    if (ee.zip && !ee.countyfips || !ee.state || factMatch(ee)) {
                        const jd = (zipDrawers[ee.zip.substring(0, 3)] || {})[ee.zip]
                        if (jd) {
                            place.countyfips = jd.fips;
                            place.state = jd.state;
                            needPatch = true;
                            const newEe = {...ees[eeIdx], countyfips: jd.fips, state: jd.state}
                            newEe.lastFacts = setFacts(newEe);
                            ees[eeIdx] = newEe;
                        }
                    }

                    if(nonMarketplaceStates.includes(place.state)) place = marketplaceDefMap[place.state]

                    const aptc = await getAptc({ household, place, household_size: 1 + (ee.married === 'Y' ? 1 : 0) + (ee.deps || 0) })(context)
                        .catch(err => {
                            console.error(`Error on employee ${ee.name || ee.uid}: ${err.message}`)
                            errs[ee.uid] = err.message;
                        })

                    // console.log('res', household);
                    if (aptc || aptc === 0) {
                        needPatch = true;
                        ees[eeIdx].ptc = aptc;
                        ees[eeIdx].ptcUpdatedAt = new Date();
                    }
                }
            }

            await Promise.all(ees.map(a => onePtc(a)))

            if (needPatch) context.result = await new CoreCall('doc-requests', context).patch(context.id as any, {employees: ees})
            context.result._fastjoin = {...context.result._fastjoin, analysis_errs: errs}
        }
        return context;
    }
}
