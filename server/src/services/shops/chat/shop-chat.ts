import {HookContext} from '../../../declarations.js';

import {addFileToVectorStore, saveChatHistory} from '../../../utils/index.js';
import {CoreCall} from 'feathers-ucan';
import OpenAi from 'openai';

//
// export const policyChat = () => {
//     return async (context: HookContext) => {
//
//         const {policy_chat} = context.params.runJoin || {};
//         if (policy_chat) {
//             const { chat_text } = policy_chat;
//             const {key, org} = context.app.get('openai');
//             const openai = new OpenAi({apiKey: key, organization: org})
//
//             const { product } = policy_chat;
//
//             const type = product.type === 'hs' ? 'shares' : 'insurance'
//
//             const exs = {
//                 'shares': 'Health shares often have limited coverage for preexisting conditions or other dollar limits on coverage - which insurance cannot have. They also have behavioral attestations that members must take seriously. The details of these are often misunderstood - either overlooked or over emphasized by competitors who want to diminish health shares.'
//             }
//
//             const getMessage = (policy:any, state:string) => {
//                 return {
//                     role: 'system',
//                     content: `You are a technical advisor in helping people find extra details about health ${type} in the state of ${state} specifically. You are discussing ${policyDetail}. The user already has a lot of information about this product, its premiums, features, and comparison to other products. They are looking for more specific contractual or network details that wouldn\'t be easy to analyze side-by-side with other options because they are nuanced. For example ${example}. Minimize general "consult a professional" disclosures, as the user already has these in view. They are just looking for specific technical information, not general disclosures.`
//                 }
//             }
//
//             const messages = [
//                 getMessage(policy, state),
//                 {
//                     role: 'user',
//                     content: `${chat_text}. Don't give general disclosures or warnings such as "speak to an advisor" or "these decisions can be complicated, be sure to consult a legal or tax professional." I already know this - I'm just looking for any technical details you can provide. Responding in either text or html format is great (if you have tables or other html elements).`
//                 }]
//             const result = await openai.chat.completions.create({
//                 messages,
//                 model: "gpt-4o"
//             })
//                 .catch(err => {
//                     console.log(`Error searching ai procedures: ${err.message}`)
//                 })
//             // console.log('result', result);
//             context.result = result;
//         }
//
//         return context;
//     }
// }


import {comparePlanTables, getShopResultTable} from './files.js';
import { getResetId } from '../sim/cost-sim.js';

export const vectorizeShop = async (context: HookContext) => {
    const {vectorize_shop} = context.params.runJoin || {};
    if (vectorize_shop) {
        const { compare_coverages } = vectorize_shop;
        const {
            coverages,
            _id,
            vectorStore
        } = context.result || {};

        if(context.result.vectorStore?.id && context.result.vectorStore.resetId && context.result.vectorStore.resetId === context.result.resetId) return context;

        let markdown = await getShopResultTable(context.result, { compare_coverages})(context);
        markdown += '## Plan Details used in simulation'

        const detail = comparePlanTables(coverages)
        markdown += detail;

        const b = Buffer.from(markdown, 'utf-8');
        const mimeType = 'text/markdown';

        const vectorRes = await addFileToVectorStore(context, {
            mimeType,
            buffer: b,
            vectorConfig: vectorStore || {},
            fileName: 'shop_docs.md',
            storeName: _id,
            taskName: 'simulator docs'
        })

        context.result = await new CoreCall('shops', context, {skipJoins: true}).patch(context.result._id, {
            $set: {
                vectorStore: {
                    id: vectorRes.vectorStoreId,
                    fileIds: [vectorRes.file.id].filter(a => !!a),
                    updatedAt: new Date(),
                    resetId: getResetId(context.result)
                }
            }
        }, {admin_pass: true, skip_hooks: true})
            .catch(err => {
                console.error(`Error saving vector store id for plan docs: ${err.message}`)
            })


    }
    return context;
}

export const shopChat = async (context: HookContext) => {
    const {shop_chat} = context.params.runJoin || {};
    if (shop_chat) {

        const {key, org} = context.app.get('openai');
        const openai = new OpenAi({apiKey: key, organization: org})

        const drawers = await new CoreCall('junk-drawers', context).find({
            query: {
                $limit: 1,
                itemId: 'ai|context'
            }
        })
        const drawer = drawers.data[0];

//         const input = `
// # Context
// I ran a simulation to measure the performance of health plans and I have a question about the results.
//
// ## Simulation Results
// ${getShopResultTable(context.result)}
//
// ## Plan Details used in simulation
// ${comparePlanTables(context.result.coverages)}
//
// # Question
// ${shop_chat.text}
//
// # Behavioral Guide
// - Use the following behavioral/bias guide for responding
// ${drawer?.data?.bias?.text}
// `;
//         const response = await openai.responses.create({
//             model: 'gpt-4o',
//             input
//             // tools: [
//                 // {
//                 //     type: 'file_search',
//                 //     vector_store_ids: [biasVectorId]
//                 // },
//                 // {
//                 //     type: 'file_search',
//                 //     vector_store_ids: [biasVectorId]
//                 // }
//             // ]
//         })
//             .catch(err => {
//                 console.error(`Error querying simulation chat: ${err.message}`)
//                 throw new Error(`Error querying simulation chat: ${err.message}`)
//             })

        const biasVectorId = drawer?.data?.bias?.vectorStore?.id;
        const vectorStoreId = context.result.vectorStore.id;
        const input = `
## 🧠 Assistant Context

A deep simulation comparing health plans has already been run. The user can view the results in a rich user interface.

- Refer to the results as **“the simulation results”**.
- Do **not** say the user provided the results — they did not.
- You may refer to plans by **name** as they appear in the UI - use names instead of plan ids in responding.

${shop_chat.current_coverage ? `- The user is currently enrolled in the plan ID ${shop_chat.current_coverage._id || shop_chat.current_coverage.id} - name: ${shop_chat.current_coverage.name}. A major purpose of this simulation is to compare this plan against individual market options.` : ''}

---

## 📊 Simulation Results Guidance

Use the **first vector store** provided for the simulation results. This contains mostly **Markdown tables** with:

- Plan comparison outcomes
- Plan details used during the simulation

When answering questions:

- ✅ **Trust the simulation results** as complete and reliable. They are the context for questions ans answers. Refer to them in answering questions to give specific data about what to do and which plans are best. 
- ✅ Use them as your **primary data source**.
- ✅ **Key to understand** the "spend" is the average spend across all years by pulling from real medical bills matching their household risk profile. Some years have no spend, some have big spend.
- ❌ Minimally speculate beyond what the simulation shows.
- ⚠️ Re-frame assumptions: Insurance trades financial risk for cost and administrative control. It is not healthcare, and rarely a net benefit except for a small minority of high-need users.

If the user asks about a plan but the answer isn’t covered in the results:

> Tell them to **click on the plan in the interface** to ask deeper questions.

## Chat History
${shop_chat.chat_history || 'N/A'}

## User Question
${shop_chat.text}

## Behavioral Guide
- Use the second vector store provided is for behavioral/bias guidance. Do not refer to the behavioral guide in your response, it's just a background guidance for context.
`;
        const response = await openai.responses.create({
            model: 'gpt-4o',
            input,
            tools: [
                {
                    type: 'file_search',
                    vector_store_ids: [vectorStoreId, biasVectorId]
                }
            ]
        })
            .catch(err => {
                console.error(`Error querying simulation chat: ${err.message}`)
                throw new Error(`Error querying simulation chat: ${err.message}`)
            })
        const output: any = response.output.filter(a => a.type === 'message');
        const content: any = output[0].content.filter(a => a.type === 'output_text')[0];

        saveChatHistory({
            chatId: shop_chat.chatId,
            subjectId: context.result._id,
            chat_session: shop_chat.chat_session,
            chat: { question: shop_chat.text, answer: content.text, annotations: content.annotations }
        })(context);

        context.result = {...context.result, _fastjoin: {...context.result._fastjoin, ai_response: content}};

    }
    return context;
}
