import {Type} from '@feathersjs/typebox';

export const sectionsSchema = Type.Optional(
    Type.Record(Type.String({ pattern: "^([0-9]|[1-9][0-9]|1[01][0-9]|120)$" }), Type.Object({
        key: Type.Optional(Type.String()),
        title: Type.Optional(Type.String()),
        sections: Type.Optional(
            Type.Record(Type.String({ pattern: "^([0-9]|[1-9][0-9]|1[01][0-9]|120)$" }), Type.Object({
                key: Type.Optional(Type.String()),
                title: Type.Optional(Type.String()),
                body: Type.Optional(Type.String())
            }))
        )
    }))
)
