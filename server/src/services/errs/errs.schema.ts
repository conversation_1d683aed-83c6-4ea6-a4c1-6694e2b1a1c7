// TypeBox schema for errs service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, commonPatch, queryWrapper } from '../../utils/common/typebox-schemas.js'

export const errsSchema = Type.Object({
  _id: ObjectIdSchema(),
  path: Type.Optional(Type.String()),
  method: Type.Optional(Type.String()),
  type: Type.Optional(Type.String()),
  id: Type.Optional(Type.String()),
  error: Type.Optional(Type.Any()),
  params: Type.Optional(Type.Any()),
  data: Type.Optional(Type.Any()),
  result: Type.Optional(Type.Any()),
  ...commonFields
}, { $id: 'Errs', additionalProperties: false })

export type Errs = Static<typeof errsSchema>
export const errsValidator = getValidator(errsSchema, dataValidator)
export const errsResolver = resolve<Errs, HookContext>({})
export const errsExternalResolver = resolve<Errs, HookContext>({})

export const errsDataSchema = Type.Object({
  ...Type.Omit(errsSchema, ['_id']).properties
}, { additionalProperties: false })

export type ErrsData = Static<typeof errsDataSchema>
export const errsDataValidator = getValidator(errsDataSchema, dataValidator)
export const errsDataResolver = resolve<ErrsData, HookContext>({})

// Pick ObjectId fields and nested ObjectId fields for query properties
const errsQueryProperties = Type.Pick(errsSchema, ['_id'])

export const errsPatchSchema = commonPatch(errsSchema, { pushPullOpts: [], pickedForSet: errsQueryProperties })
export type ErrsPatch = Static<typeof errsPatchSchema>
export const errsPatchValidator = getValidator(errsPatchSchema, dataValidator)
export const errsPatchResolver = resolve<ErrsPatch, HookContext>({})

export const errsQuerySchema = queryWrapper(errsSchema)
export type ErrsQuery = Static<typeof errsQuerySchema>
export const errsQueryValidator = getValidator(errsQuerySchema, queryValidator)
export const errsQueryResolver = resolve<ErrsQuery, HookContext>({})
